package com.qudian.toy.gateway.repository.remote.http;

import com.qudian.toy.gateway.repository.config.ToolRemoteApiConfig;
import com.qudian.toy.gateway.repository.enums.ErrorCodeEnum;
import com.qudian.toy.gateway.repository.exception.BizRuntimeErrorException;
import com.qudian.toy.gateway.repository.wrapper.FileStreamWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <p>文件名称:com.qudian.lme.driver.gateway.repository.remote.http.ToolServiceImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/11/15
 */
@Service
@Slf4j
public class ToolServiceImpl {
    @Resource
    private ToolRemoteApiConfig remoteConfig;
    @Resource
    private FileStreamWrapper fileStreamWrapper;

    public Object toolProxy(HttpServletRequest request, String path, String inJSON, Headers headers) {
        log.info("[tool-proxy]path:{}, in_json:{}", path, inJSON);
        try {
            if (request.getContentType().startsWith(MultipartBody.FORM.toString())) {
                return fileStreamWrapper.fileUpload(request, remoteConfig.getHost() + path, headers);
            }
            String responseBody = this.post(path, inJSON);
            log.info("[tool-proxy]response body:{}", responseBody);
            return responseBody;
        } catch (Exception e) {
            log.error("[tool-proxy]an error occurs", e);
            throw new BizRuntimeErrorException(ErrorCodeEnum.PUSH_TOKEN_UPLOAD);
        }
    }

    private String post(String uri, String postJsonData) throws IOException {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        Request request = new Request.Builder()
                .url(remoteConfig.getHost() + uri)
                .post(RequestBody.create(mediaType, postJsonData))
                .build();
        Response response = this.newCallHttp(request);
        if (!response.isSuccessful()) {
            throw new IOException("Unexpected code " + response);
        }
        if (null == response.body()) {
            throw new BizRuntimeErrorException(ErrorCodeEnum.RESPONSE_BODY_MISSING);
        }
        return response.body().string();
    }

    private Response newCallHttp(Request request) throws IOException {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();
        return okHttpClient.newCall(request).execute();
    }

}
