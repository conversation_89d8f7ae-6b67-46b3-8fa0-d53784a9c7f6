package com.qudian.toy.gateway.repository.exception;

import com.qudian.lme.common.exception.InfoException;
import com.qudian.toy.gateway.repository.enums.ErrorCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NoFundApiException extends InfoException {

    public NoFundApiException(ErrorCodeEnum errorCode) {
        super(errorCode.getCode(), errorCode.getDesc(), errorCode.isDisplay());
    }
}
