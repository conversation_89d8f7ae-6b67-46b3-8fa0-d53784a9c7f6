package com.qudian.toy.gateway.repository.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import okhttp3.RequestBody;

import javax.annotation.Nullable;

/**
 * @Author: yang<PERSON><PERSON>
 * @Date: 2023/12/15
 * @Version: 1.0.0
 **/
@Data
@Accessors(chain = true)
public class HttpMultiPartDTO {
    /**
     * form name
     */
    private String name;

    @Nullable
    private String fileName;

    @Nullable
    private RequestBody requestBody;

    @Nullable
    private String requstBodyString;
}
