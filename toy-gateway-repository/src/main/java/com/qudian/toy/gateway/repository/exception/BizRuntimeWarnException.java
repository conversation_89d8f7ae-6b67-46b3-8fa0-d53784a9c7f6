package com.qudian.toy.gateway.repository.exception;

import com.qudian.lme.common.exception.WarningException;
import com.qudian.toy.gateway.repository.enums.ErrorCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BizRuntimeWarnException extends WarningException {

    public BizRuntimeWarnException(ErrorCodeEnum errorCode) {
        super(errorCode.getCode(), errorCode.getDesc(), errorCode.isDisplay());
    }
}
