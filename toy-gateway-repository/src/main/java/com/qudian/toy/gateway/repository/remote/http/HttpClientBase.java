package com.qudian.toy.gateway.repository.remote.http;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;

import java.util.concurrent.TimeUnit;

/**
 * <p>文件名称:com.qudian.lme.driver.common.utils.common.HttpClientBase</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/10/12
 */
public abstract class HttpClientBase {
    protected OkHttpClient okHttpClient;

    public HttpClientBase(long connectionTimeout, long readTimeout, int MAX_IDLE_CONNECTIONS, long KEEP_ALIVE_DURATION_MINUTE, int MAX_REQUESTS) {
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(connectionTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(false)
                .connectionPool(initPool(MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION_MINUTE))
                // 动态修改超时时间
                .addInterceptor(new TimeoutInterceptor())
                .retryOnConnectionFailure(true)
                .build();
        okHttpClient.dispatcher().setMaxRequests(MAX_REQUESTS);
        okHttpClient.dispatcher().setMaxRequestsPerHost(MAX_REQUESTS);
    }

    private ConnectionPool initPool(int MAX_IDLE_CONNECTIONS, long KEEP_ALIVE_DURATION_MINUTE) {
        return new ConnectionPool(MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION_MINUTE, TimeUnit.MINUTES);
    }
}
