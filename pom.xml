<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.1</version>
    </parent>
    <groupId>com.qudian.toy</groupId>
    <artifactId>toy-api-gateway</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>toy-gateway-server</module>
        <module>toy-gateway-core</module>
        <module>toy-gateway-repository</module>
        <module>toy-gateway-api</module>
    </modules>

    <properties>
        <matrix-adapter.version>3.1.0-jdk17-RELEASE</matrix-adapter.version>
        <toy.gateawy.version>1.0.0-SNAPSHOT</toy.gateawy.version>
        <sentry.version>1.7.30</sentry.version>
        <apollo.version>2.2.0</apollo.version>
        <fastjson-newest.version>2.0.51</fastjson-newest.version>
        <guava.version>33.2.1-jre</guava.version>
        <prometheus.version>1.13.1</prometheus.version>
        <jwt.version>4.4.0</jwt.version>
        <dubbo.version>3.2.14</dubbo.version>
        <commons-io.version>2.16.1</commons-io.version>
        <hutool-all.version>5.8.28</hutool-all.version>
        <junit.api.version>5.10.2</junit.api.version>
        <toy.app.api.version>1.0.1-SNAPSHOT</toy.app.api.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.qudian.lme</groupId>
                <artifactId>matrix-adapter</artifactId>
                <version>${matrix-adapter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.toy</groupId>
                <artifactId>toy-gateway-core</artifactId>
                <version>${toy.gateawy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.toy</groupId>
                <artifactId>toy-gateway-repository</artifactId>
                <version>${toy.gateawy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.toy</groupId>
                <artifactId>toy-app-api</artifactId>
                <version>${toy.app.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper-curator5</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-core</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${prometheus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson-newest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.api.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.qudian.toy</groupId>
                <artifactId>toy-gateway-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--多环境配置begin-->
    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profilesActive>local</profilesActive>
                <dockerTag>local</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profilesActive>dev</profilesActive>
                <dockerTag>dev</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profilesActive>test</profilesActive>
                <dockerTag>test</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>test2</id>
            <properties>
                <profilesActive>test2</profilesActive>
                <dockerTag>test2</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profilesActive>prod</profilesActive>
                <dockerTag>latest</dockerTag>
                <hurbo></hurbo>
            </properties>
        </profile>
    </profiles>
    <!--多环境配置end-->

    <build>
        <plugins>
            <!-- docker -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.4.13</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <includeEmptyDirs>true</includeEmptyDirs>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <!--maven仓库配置begin-->
    <distributionManagement>
        <repository>
            <!-- ID要和MAVEN中conf/setting.xml 中的server保持一致 -->
            <id>releases</id>
            <name>User Project Release</name>
            <!-- release版本的url地址 -->
            <url>http://120.26.50.3:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>User Project SNAPSHOTS</name>
            <url>http://120.26.50.3:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>public</id>
            <name>public repository</name>
            <url>http://120.26.50.3:8081/repository/maven-public/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
        <repository>
            <id>snapshots</id>
            <name>snapshots repository</name>
            <url>http://120.26.50.3:8081/repository/maven-snapshots/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
        <repository>
            <id>releases</id>
            <name>releases repository</name>
            <url>http://120.26.50.3:8081/repository/maven-releases/</url>
            <releases>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>qudian</id>
            <name>qudian maven</name>
            <url>http://120.26.50.3:8081/repository/maven-releases/</url>
            <releases>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <!--maven仓库配置end-->
</project>
