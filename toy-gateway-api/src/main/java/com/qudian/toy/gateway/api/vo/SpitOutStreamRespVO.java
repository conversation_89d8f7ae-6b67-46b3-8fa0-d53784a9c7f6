package com.qudian.toy.gateway.api.vo;

import java.io.Serializable;

/**
 * <p>文件名称:com.qudian.toy.gateway.api.vo.SpitOutStreamRespVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2024/6/20
 */
public class SpitOutStreamRespVO implements Serializable {
    private String deviceId;
    private boolean finish;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public boolean isFinish() {
        return finish;
    }

    public void setFinish(boolean finish) {
        this.finish = finish;
    }
}
