package com.qudian.toy.gateway.api.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>文件名称:com.qudian.toy.gateway.api.vo.StreamJSONFormatVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2024/7/1
 */
@Data
public class StreamJSONFormatVO implements Serializable {
    private String type;
    private String sn;
    private String id;
    private Long timestamp;
    private StreamSubMetadata metadata;
    private StreamSubData data;


    @Data
    public static class StreamSubMetadata implements Serializable {
        private String encoding;
        private String sampleRate;
    }

    @Data
    public static class StreamSubData implements Serializable {
        private Integer index;
        private Integer status;
        private byte[] audio;
        private String text;
    }
}
