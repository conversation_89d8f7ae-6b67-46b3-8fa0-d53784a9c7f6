package com.qudian.toy.gateway.api.facade;


import com.qudian.toy.gateway.api.vo.SpitOutStreamReqVO;
import com.qudian.toy.gateway.api.vo.SpitOutStreamRespVO;
import org.apache.dubbo.common.stream.StreamObserver;

/**
 * <p>文件名称:com.qudian.gateway.api.facade.SpitOutStreamFacade</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2024/6/20
 */
public interface SpitOutStreamFacade {

    StreamObserver<SpitOutStreamReqVO> spitOutStream(StreamObserver<SpitOutStreamRespVO> streamObserver);
}
