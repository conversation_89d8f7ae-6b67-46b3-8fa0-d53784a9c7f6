<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.qudian.toy.gateway</groupId>
  <artifactId>toy-gateway-api</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <description>Parent pom providing dependency and plugin management for applications built with <PERSON><PERSON></description>
  <url>https://spring.io/projects/spring-boot/toy-api-gateway/toy-gateway-api</url>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>Spring</name>
      <email><EMAIL></email>
      <organization>VMware, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/spring-projects/spring-boot/toy-api-gateway/toy-gateway-api</url>
  </scm>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo</artifactId>
      <version>3.2.14</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.32</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.2.7</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>bom</flattenMode>
              <pomElements>
                <pluginManagement>keep</pluginManagement>
                <profiles>keep</profiles>
                <properties>keep</properties>
                <distributionManagement>keep</distributionManagement>
                <build>keep</build>
                <repositories>keep</repositories>
              </pomElements>
            </configuration>
          </execution>
          <execution>
            <id>flatten-clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
    </plugins>
  </build>
</project>
