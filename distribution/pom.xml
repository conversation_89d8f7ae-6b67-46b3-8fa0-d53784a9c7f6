<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-all</artifactId>
        <version>5.3.4-SNAPSHOT</version>
    </parent>
    <artifactId>rocketmq-distribution</artifactId>
    <name>rocketmq-distribution ${project.version}</name>
    <packaging>pom</packaging>

    <properties>
        <project.root>${basedir}/..</project.root>
    </properties>

    <profiles>
        <profile>
            <id>release-all</id>
            <dependencies>
                <dependency>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-container</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-controller</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-broker</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-proxy</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-client</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-tools</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-example</artifactId>
                </dependency>
            </dependencies>

            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>release-all</id>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <descriptors>
                                        <descriptor>release.xml</descriptor>
                                    </descriptors>
                                    <appendAssemblyId>false</appendAssemblyId>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <finalName>rocketmq-${project.version}</finalName>
            </build>
        </profile>

        <profile>
            <id>release-client</id>
            <dependencies>
                <dependency>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-client</artifactId>
                </dependency>
            </dependencies>

            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>release-client</id>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <descriptors>
                                        <descriptor>release-client.xml</descriptor>
                                    </descriptors>
                                    <appendAssemblyId>false</appendAssemblyId>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <finalName>rocketmq-client-${project.version}</finalName>
            </build>
        </profile>
    </profiles>

</project>