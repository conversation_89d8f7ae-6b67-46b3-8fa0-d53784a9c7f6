package com.qudian.toy.gateway.server.controller;

import com.qudian.lme.base.constant.BaseConstant;
import com.qudian.lme.base.vo.BaseResponseVo;
import com.qudian.lme.common.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.toy.gateway.server.controller.ControllerExceptionAspect</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2024/7/3
 */
@ControllerAdvice
@Slf4j
public class ControllerExceptionAspect {

    private String getRequestURL() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return requestAttributes.getRequest().getRequestURL().toString();
    }

    /**
     * 统一处理Exception异常
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(Exception.class)
    public BaseResponseVo defaultExceptionHandler(Exception e) {
        log.error("请求地址：" + getRequestURL() + "，发生未知异常", e);
        return BaseResponseVo.builder()
                .code(BaseConstant.SYSTEM_ERROR_CODE)
                .message(BaseConstant.SYSTEM_ERROR_MESSAGE)
                .displayable(false)
                .build();
    }

    /**
     * 统一处理入参异常 MethodArgumentNotValidException
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public BaseResponseVo methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        List<String> errorInformation = e.getBindingResult().getAllErrors()
                .stream()
                .map(ObjectError::getDefaultMessage)
                .collect(Collectors.toList());
        log.info("请求地址：{}，入参异常：{}", getRequestURL(), errorInformation.toString());
        return BaseResponseVo.builder()
                .code(BaseConstant.INVALID_PARAMETER_CODE)
                .message(errorInformation.toString())
                .displayable(true)
                .build();
    }

    /**
     * 业务异常处理
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(BaseException.class)
    public BaseResponseVo bizRuntimeExceptionHandler(BaseException e) {
        log.info("请求地址：" + getRequestURL() + "，发生业务异常", e);
        if (true == e.isDisplayable()) {
            return BaseResponseVo.builder()
                    .code(e.getCode())
                    .message(e.getMessage())
                    .displayable(e.isDisplayable())
                    .build();
        } else {
            return BaseResponseVo.builder()
                    .code(BaseConstant.SYSTEM_ERROR_CODE)
                    .message(BaseConstant.SYSTEM_ERROR_MESSAGE)
                    .displayable(false)
                    .build();
        }
    }


}
