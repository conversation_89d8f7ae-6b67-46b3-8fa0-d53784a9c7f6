package com.qudian.toy.gateway.server;

import cn.hutool.core.io.resource.ResourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.TrueFileFilter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

import java.io.File;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.List;
import java.util.Random;

/**
 * <p>文件名称:com.qudian.lme.server.StartServerApplication</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/10/20
 */
@ComponentScan(basePackages = {"com.qudian.**.*"})
@EnableScheduling
@SpringBootApplication
@EnableWebSocket
@Slf4j
public class StartServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(StartServerApplication.class, args);

        String folderPath = "static";
        URL resource = ResourceUtil.getResource(folderPath);
        if (null == resource) {
            //文件夹下不存在文件
            log.warn("random-file-selector.{}:No file exists in the folder.", folderPath);
            return ;
        }
        // 随机选择一个文件
        File directory = null;
        try {
            directory = new File(resource.toURI());

            // 检查目录是否存在且是目录
            if (!directory.exists() || !directory.isDirectory()) {
                System.out.println("指定的路径不存在或不是一个目录");
                return;
            }
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        List<File> filesList = (List<File>) FileUtils.listFiles(directory, TrueFileFilter.INSTANCE, TrueFileFilter.INSTANCE);
        // 检查是否有文件
        if (filesList.isEmpty()) {
            System.out.println("目录为空");
            return;
        }


        File selectedFile = filesList.get(new Random().nextInt(filesList.size()));

        log.info("random-file-selector.{}:Selected file:{}", folderPath, selectedFile);
    }
}
