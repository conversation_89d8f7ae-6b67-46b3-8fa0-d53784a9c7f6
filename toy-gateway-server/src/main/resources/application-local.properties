server.port=60066
app.id=${spring.application.name}

logging.config=classpath:log4j2-spring-local.xml
logging.level.com.qudian.wanwu.shop.tripod.dao=debug

swagger.enable = true
aes.key = asd12311kijoudad
aes.iv = 5e8y6w45ju8w9jq8
aes.key.web = CGl5h8Ls9veNVuYg
aes.iv.web = yfnUr8OgvGs6RJYl
web.secure.services = partner
application.ding.warningFrequency = {"local":[1,9],"dev":[2,5,9],"test":[2,4,7],"test2":[2,4,7],"prod":[1,3,5,9]}
application.ding.webUrl = https://oapi.dingtalk.com/robot/send?access_token=
application.ding.warningProfiles = local-
business.config.switch = false
sign.key = u1b92ajeyxtexumb
sign.key.web = L3d70cvds2UZBGX6
sign.enable = true
sign.wpp.version = 1.0.73
sign.app.version = 1.4.0
replaySwitch.enable = false
replaySwitch.app.version = 1.7.15
replaySwitch.wpp.version = 1.6.0
application.channel = qfood
FORMAT_MESSAGES_PATTERN_DISABLE_LOOKUPS = true

spring.jedis.host = lme-dev1-o.redis.zhangbei.rds.aliyuncs.com
spring.jedis.block-when-exhausted = false
spring.jedis.database = 0
spring.jedis.port = 6379
spring.jedis.password = JQgFNrilA86vGRjh
spring.jedis.jedis.pool.max-active = 200
spring.jedis.jedis.pool.max-wait = 10000
spring.jedis.jedis.pool.max-idle = 8
spring.jedis.jedis.pool.min-idle = 0
spring.jedis.timeout = 10000
spring.cache.redis.time-to-live = 600

#management.server.port = 9147
#management.server.servlet.context-path = ${server.servlet.context-path}
#management.endpoints.web.exposure.include = health,metrics,prometheus
#management.metrics.tags.application = ${spring.application.name}
#management.metrics.distribution.percentiles-histogram.http.server.requests = true

ok.http.connect-timeout = 30
ok.http.read-timeout = 30
ok.http.write-timeout = 30
ok.http.max-idle-connections = 5
ok.http.keep-alive-duration = 5

dubbo.application.name = ${spring.application.name}
dubbo.registry.address = zookeeper://*************:32181
dubbo.registry.timeout = 20000
dubbo.protocol.name = tri
dubbo.protocol.port = 20980
dubbo.scan.base-packages = com.qudian.*
dubbo.consumer.timeout = 500000
dubbo.consumer.check = false
dubbo.registry.check = false
#dubbo.provider.version = 1.0.0
#dubbo.provider.validation = true
#dubbo.provider.filter = -exception,dubboExceptionFilter,default
#dubbo.consumer.version = 1.0.0
dubbo.protocol.threads = 200

zk.host = *************:32181
zk.connectionTimeout = 5000
zk.sessionTimeout = 60000
zk.event.path = /app/apiVersion

lme.tool.host = http://**************:30080
lme.robot.host = http://**************:30083
