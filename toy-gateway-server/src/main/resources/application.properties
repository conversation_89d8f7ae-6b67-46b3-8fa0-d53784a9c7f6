spring.application.name=toy-api-gateway
server.servlet.context-path=/
spring.profiles.active=@profilesActive@
spring.main.allow-bean-definition-overriding=true
server.port=8080

## servlet.multipart settings
spring.servlet.multipart.max-file-size=10240KB
spring.servlet.multipart.max-request-size=10240KB

#tomcatçaccesslogæ¥å¿
#server.tomcat.accesslog.buffered=false
#server.tomcat.accesslog.enabled=true
#server.tomcat.accesslog.file-date-format=-yyyyMMdd
#server.tomcat.accesslog.pattern=%{x-true-ip}i - %l %{begin}t "%r" %T %s %b "%{Referer}i" "%{User-Agent}i" "%{x-original-forwarded-for}i" %h %{x-trace-id}o %I %{msec}t
#server.tomcat.accesslog.prefix=access
#server.tomcat.accesslog.rename-on-rotate=false
#server.tomcat.accesslog.request-attributes-enabled=false
#server.tomcat.accesslog.rotate=true
#server.tomcat.accesslog.suffix=.log
#server.tomcat.accesslog.directory=toy-api-gateway
#server.tomcat.basedir=/data/logs/driver/
