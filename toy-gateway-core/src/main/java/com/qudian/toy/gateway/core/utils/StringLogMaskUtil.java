package com.qudian.toy.gateway.core.utils;

import org.springframework.util.StringUtils;

/**
 * @Description 字符串脱敏工具类
 * <AUTHOR>
 * @Date 2021/7/12 6:51 下午
 */
public class StringLogMaskUtil {


    /**
     * 手机号码前三后四脱敏
     */
    public static String mobileEncrypt(String mobile) {
        if (StringUtils.isEmpty(mobile) || (mobile.length() != 11)) {
            return mobile;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }


    /**
     * 身份证前三后四脱敏
     */
    public static String idEncrypt(String id) {
        if (StringUtils.isEmpty(id) || (id.length() < 18)) {
            return id;
        }
        return id.replaceAll("(?<=\\w{3})\\w(?=\\w{4})", "*");
    }
}
