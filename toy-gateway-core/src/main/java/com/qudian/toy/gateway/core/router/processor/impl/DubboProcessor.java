package com.qudian.toy.gateway.core.router.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.qudian.toy.gateway.center.api.enums.RouteApiTypeEnum;
import com.qudian.toy.gateway.core.bean.DubboRouteApi;
import com.qudian.toy.gateway.core.bean.RouterContext;
import com.qudian.toy.gateway.core.router.processor.ApiProcessor;
import com.qudian.toy.gateway.repository.enums.ErrorCodeEnum;
import com.qudian.toy.gateway.repository.exception.BizRuntimeInfoException;
import com.qudian.toy.gateway.repository.exception.NoFundApiException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.utils.SimpleReferenceCache;
import org.apache.dubbo.rpc.service.GenericService;
import org.apache.dubbo.spring.boot.autoconfigure.DubboConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Slf4j
@Component
public class DubboProcessor implements ApiProcessor {

    private static final Integer DEFAULT_TIME_OUT = 5000;

    @Resource
    private DubboConfigurationProperties properties;

    private final SimpleReferenceCache defaultReferenceConfigCache = SimpleReferenceCache.getCache(SimpleReferenceCache.DEFAULT_NAME, referenceConfig -> {
        String iName = referenceConfig.getInterface();
        if (StringUtils.isBlank(iName)) {
            Class<?> clazz = referenceConfig.getInterfaceClass();
            iName = clazz.getName();
        }
        if (StringUtils.isBlank(iName)) {
            throw new IllegalArgumentException("No interface info in ReferenceConfig " + referenceConfig);
        }
        StringBuilder ret = new StringBuilder();
        if (!StringUtils.isBlank(referenceConfig.getGroup())) {
            ret.append(referenceConfig.getGroup()).append("/");
        }
        ret.append(iName);
        if (null != referenceConfig.getTimeout()) {
            ret.append("/").append(referenceConfig.getTimeout());
        }
        if (!StringUtils.isBlank(referenceConfig.getVersion())) {
            ret.append(":").append(referenceConfig.getVersion());
        }
        return ret.toString();
    });

    @Override
    public RouteApiTypeEnum support() {
        return RouteApiTypeEnum.DUBBO;
    }

    /**
     * 处理调用
     */
    @Override
    public void process(RouterContext context) {
        DubboRouteApi method = context.getDubboRouteApi();
        GenericService service = getService(method);
        if (service == null) {
            throw new NoFundApiException(ErrorCodeEnum.API_NO_SUPPORT);
        }
        context.setResult(invoke(context, method, service));
    }

    private GenericService getService(DubboRouteApi method) {
        String interfaceClazz = method.getInterfaceClazz();
        if (StringUtils.isBlank(interfaceClazz)) {
            return null;
        }
        ReferenceConfig<GenericService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setRegistry(properties.getRegistry());
        referenceConfig.setInterface(interfaceClazz);
        referenceConfig.setVersion(method.getVersion());
        referenceConfig.setGroup(method.getGroup());
        referenceConfig.setGeneric("true");
        referenceConfig.setTimeout(method.getTimeout() != null ? method.getTimeout() : DEFAULT_TIME_OUT);
        referenceConfig.setCheck(false);
        referenceConfig.setRetries(0);
        GenericService genericService = defaultReferenceConfigCache.get(referenceConfig);
        log.debug("ReferenceConfigCache [{}] cached.", interfaceClazz);
        return genericService;
    }

    @SuppressWarnings("unchecked")
    private Object invoke(RouterContext context, DubboRouteApi method, GenericService service) {
        String requestClazz = method.getRequestClazz();
        JSONObject requestBody = context.getRequestBody();
        String[] parameterTypes = {};
        Object[] parameters = {};
        if (!StringUtils.isBlank(requestClazz)) {
            parameterTypes = new String[]{requestClazz};
        }

        // 兼容有参函数，请求对象默认为{}的情况
        if (!ObjectUtils.isEmpty(requestBody) || (ObjectUtils.isEmpty(requestBody) && parameterTypes.length == 1)) {
            parameters = new Object[]{requestBody};
        }

        if (parameters.length != parameterTypes.length) {
            log.info("DubboProcessor#invoke PARAMETERS_ERROR,parameters:{},parameterTypes:{}", parameters, parameterTypes);
            throw new BizRuntimeInfoException(ErrorCodeEnum.PARAMETERS_ERROR);
        }
        return service.$invoke(method.getInterfaceMethod(), parameterTypes, parameters);
    }

}
