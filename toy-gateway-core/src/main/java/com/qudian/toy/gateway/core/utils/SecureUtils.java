package com.qudian.toy.gateway.core.utils;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * 将加解密方法，抽成一个工具类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SecureUtils {

    /**
     * 使用默认的UTF-8编码，加密位数128，加解码方式hex
     * 加密模式CBC，偏移量iv，秘钥key
     */
    /**
     * pkcs5补码填充算法
     */
    private final Padding padding = Padding.PKCS5Padding;

    /**
     * 加密模式 CBC
     */
    private final Mode mode = Mode.CBC;
    /**
     * 秘钥
     */
    @Value("${aes.key}")
    private String key;
    /**
     * 偏移量
     */
    @Value("${aes.iv}")
    private String iv;

    public AES aes;

    @PostConstruct
    public void generateAes() {
        log.info("aes generateAes key: {}, iv:{}", key, iv);
        this.aes = new AES(mode, padding, key.getBytes(), iv.getBytes());
    }

    public String decryptStr(String data) {
        return aes.decryptStr(data);
    }

    public String encryptHex(String data) {
        return this.aes.encryptHex(data);
    }

}
