package com.qudian.toy.gateway.core.router.processor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.toy.gateway.center.api.dto.request.DubboRouteApiRequestDTO;
import com.qudian.toy.gateway.center.api.dto.response.DubboRouteApiResponseDTO;
import com.qudian.toy.gateway.center.api.facade.DubboRouteApiRemoteFacade;
import com.qudian.toy.gateway.core.bean.ApiInfo;
import com.qudian.toy.gateway.core.bean.DubboRouteApi;
import com.qudian.toy.gateway.core.constant.RpcConstants;
import com.qudian.toy.gateway.repository.enums.ErrorCodeEnum;
import com.qudian.toy.gateway.repository.exception.NoFundApiException;
import com.qudian.toy.gateway.core.utils.BeanCopyUtil;
import com.qudian.toy.gateway.core.utils.DingWarningAlert;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/9/8
 **/
@Slf4j
@Component
public class ApiMethodRouter {

    @Reference(version = "1.0.0")
    private DubboRouteApiRemoteFacade apiRemoteService;

    private static volatile Map<ApiInfo, DubboRouteApi> CONFIG = Maps.newHashMap();

    @Resource
    private DingWarningAlert dingWarningAlert;

    private final String dividedSymbols = "/";

    /**
     * 根据api信息获取dubbo方法
     *
     * @param apiInfo
     */
    public DubboRouteApi router(ApiInfo apiInfo) {
        checkApi(apiInfo);
        DubboRouteApi dubboRouteApi = CONFIG.get(apiInfo);
        if (dubboRouteApi == null) {
            throw new NoFundApiException(ErrorCodeEnum.API_NO_SUPPORT);
        }
        return dubboRouteApi;
    }

    /**
     * 检查api信息
     */
    private void checkApi(ApiInfo apiInfo) {
        if (apiInfo == null) {
            throw new NoFundApiException(ErrorCodeEnum.API_NO_SUPPORT);
        }
        if (StringUtils.isBlank(apiInfo.getService())) {
            throw new NoFundApiException(ErrorCodeEnum.API_NO_SUPPORT);
        }
    }

    @PostConstruct
    public void init() {
        reloadConfig();
    }

    //TODO@CH
    @Scheduled(cron = "0 0/2 * * * ? ")
    public void refresh() {
        reloadConfig();
    }

    public void refreshByVersion(String version) {
        log.info("api version update, version:[{}]", version);
        reloadConfig();
    }

    private synchronized void reloadConfig() {
        List<DubboRouteApi> routeApis = getDubboRouteConfig();
        if (CollectionUtils.isEmpty(routeApis)) {
            dingWarningAlert.reloadFailWarning();
            return;
        }
        CONFIG = routeApis.stream().collect(Collectors.toMap(m -> new ApiInfo(m.getHttpMethod(), m.getService(), m.getApi()), Function.identity()));
        log.info("dubbo route api config reload success!");
    }

    private List<DubboRouteApi> getDubboRouteConfig() {
        try {
            BaseResponseDTO<List<DubboRouteApiResponseDTO>> responseDTO = apiRemoteService.getDubboRouteApiList(new DubboRouteApiRequestDTO());
            if (!RpcConstants.SUCCESS_CODE.equals(responseDTO.getCode())) {
                log.error("dubbo route api config fetch fail, message:[{}]", responseDTO.getMessage());
                return Collections.emptyList();
            }
            List<DubboRouteApiResponseDTO> routeApiList = responseDTO.getData();
            if (CollectionUtils.isEmpty(routeApiList)) {
                return Collections.emptyList();
            }
            return map(routeApiList);
        } catch (Exception e) {
            log.error("dubbo route api config fetch fail", e);
            return Collections.emptyList();
        }
    }

    private List<DubboRouteApi> map(List<DubboRouteApiResponseDTO> routeApiList) {
        return routeApiList.stream().map(r -> {
            DubboRouteApi dubboRouteApi = new DubboRouteApi();
            BeanUtils.copyProperties(r, dubboRouteApi);

            String api = dubboRouteApi.getApi();
            if (!StringUtils.isBlank(api) && !api.startsWith(dividedSymbols)) {
                dubboRouteApi.setApi(dividedSymbols + api);
            }
            dubboRouteApi.trim();
            List<DubboRouteApi.RateLimiterConfig> target = Lists.newArrayList();
            BeanCopyUtil.listCopy(r.getRateLimiterConfigs(), target, DubboRouteApi.RateLimiterConfig.class);
            dubboRouteApi.setRateLimiterConfigs(target);
            // 初始化配置，过滤无效配置。兼容历史限流

            initRateLimiterConfig(dubboRouteApi);
            return dubboRouteApi;
        }).collect(Collectors.toList());
    }

    /**
     * 初始化配置数据
     *
     * @param dubboRouteApi
     */
    private void initRateLimiterConfig(DubboRouteApi dubboRouteApi) {
        List<DubboRouteApi.RateLimiterConfig> rateLimiterConfigs = dubboRouteApi.getRateLimiterConfigs();
        if (rateLimiterConfigs == null) {
            rateLimiterConfigs = Lists.newArrayList();
        }

        // 兼容旧版，注意旧版的时间单位是2s
        if (dubboRouteApi.needFrequency()) {
            dubboRouteApi.getRateLimiterConfigs().add(DubboRouteApi.RateLimiterConfig.builder()
                    .expression(RpcConstants.DEFAULT_RATE_LIMITER_EXPRESSION)
                    .frequency(dubboRouteApi.getFrequency().longValue())
                    // 原数据为2秒
                    .period(2000L)
                    .build()
            );
        }

        rateLimiterConfigs = rateLimiterConfigs.stream().filter(this::isValidRateLimiterConfig).collect(Collectors.toList());
        dubboRouteApi.setRateLimiterConfigs(rateLimiterConfigs);
    }

    private boolean isValidRateLimiterConfig(DubboRouteApi.RateLimiterConfig config) {
        return config.getPeriod() != null && config.getPeriod() > 0 &&
                StringUtils.isNotEmpty(config.getExpression())
                && config.getFrequency() != null && config.getFrequency() > 0;
    }
}
