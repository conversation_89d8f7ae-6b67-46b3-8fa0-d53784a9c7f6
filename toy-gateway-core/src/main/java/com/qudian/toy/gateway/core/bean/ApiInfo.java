package com.qudian.toy.gateway.core.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 前端API信息
 *
 * <AUTHOR>
 * @Date 2020/9/8
 **/
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Data
public class ApiInfo {
    /**
     * http method
     */
    private String method;
    /**
     * 服务
     */
    private String service;
    /**
     * path api
     */
    private String api;

    public ApiInfo(RequestMethod method, String service, String api) {
        this.method = method.name();
        this.service = service;
        this.api = api;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
