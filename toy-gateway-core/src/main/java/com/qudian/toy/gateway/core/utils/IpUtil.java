package com.qudian.toy.gateway.core.utils;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Splitter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 */
@Slf4j
public class IpUtil {

    public static final String X_ORIGINAL_FORWARDED_FOR = "x-original-forwarded-for";
    public static final String X_FORWARDED_FOR = "x-forwarded-for";
    public static final String WL_PROXY_CLIENT_IP = "wl-proxy-client-ip";
    public static final String X_TRUE_IP = "x-true-ip";


    /**
     * 获取客户端请求的真实IP
     * octo转发Ip地址header数据
     * 优先级顺序
     * x-original-forwarded-for 第一个IP
     * x-forwarded-for 第一个IP
     * <p>
     * 例：
     * x-true-ip = ************
     * wl-proxy-client-ip = ************
     * x-original-forwarded-for = ************, ***********, **************
     * remoteip = ***********
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ipAddress = "";
        try {
            log.info("x-original-forwarded-for:{}, x-forwarded-for:{}, x-true-ip:{}, wl-proxy-client-ip:{}, "
                    , request.getHeader(X_ORIGINAL_FORWARDED_FOR)
                    , request.getHeader(X_FORWARDED_FOR)
                    , request.getHeader(X_TRUE_IP)
                    , request.getHeader(WL_PROXY_CLIENT_IP));
            ipAddress = request.getHeader(X_ORIGINAL_FORWARDED_FOR);

            ipAddress = findFirstIp(request.getHeader(X_ORIGINAL_FORWARDED_FOR));
            if (!ipAddress.isEmpty()) {
                return ipAddress;
            }
            ipAddress = findFirstIp(request.getHeader(X_FORWARDED_FOR));
            if (!ipAddress.isEmpty()) {
                return ipAddress;
            }
            ipAddress = request.getHeader(X_TRUE_IP);
            if (!StrUtil.isBlank(ipAddress)) {
                return ipAddress;
            }
            ipAddress = request.getHeader(WL_PROXY_CLIENT_IP);
            if (!StrUtil.isBlank(ipAddress)) {
                return ipAddress;
            }
        } catch (Exception e) {
            log.error("getIpAddr error", e);
        }
        return ipAddress;
    }

    private static boolean isEmpty(String ipAddress) {
        return StrUtil.isEmpty(ipAddress) || "unknown".equalsIgnoreCase(ipAddress);
    }

    private static String findFirstIp(String ipAddress) {
        if (isEmpty(ipAddress)) {
            return "";
        }
        return Splitter.on(",").splitToStream(ipAddress).findFirst().orElse("");
    }
}
