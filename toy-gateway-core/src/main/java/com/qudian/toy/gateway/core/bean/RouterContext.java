package com.qudian.toy.gateway.core.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qudian.toy.gateway.core.constant.RpcConstants;
import com.qudian.toy.gateway.repository.enums.ErrorCodeEnum;
import com.qudian.toy.gateway.repository.exception.BizRuntimeInfoException;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * Router上下文
 *
 * <AUTHOR>
 * @Date 2020/9/8
 **/
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Slf4j
public class RouterContext {

    /**
     * api 信息
     */
    private ApiInfo apiInfo;
    /**
     * 目标dubbo方法
     */
    private DubboRouteApi dubboRouteApi;
    /**
     * 请求body
     */
    private JSONObject requestBody;
    /**
     * 请求body json
     */
    private String jsonBodyString;
    /**
     * 调用结果
     */
    private Object result;
    /**
     * 请求param
     */
    private String queryString;

    public RouterContext(ApiInfo apiInfo, String jsonBodyString) {
        this.apiInfo = apiInfo;
        this.jsonBodyString = jsonBodyString;
        if (!StringUtils.isEmpty(jsonBodyString)) {
            try {
                requestBody = JSONObject.parseObject(jsonBodyString);
            } catch (Exception e) {
                throw new BizRuntimeInfoException(ErrorCodeEnum.PARAMETERS_ERROR);
            }
        }
    }

    public void setAuthUserId(Long userId) {
        if (ObjectUtils.isEmpty(this.requestBody)) {
            this.requestBody = new JSONObject() {
            };
        }
        this.requestBody.put(RpcConstants.USER_ID, userId);
    }

    public void setAuthUserToyId(String userToyId) {
        if (ObjectUtils.isEmpty(this.requestBody)) {
            this.requestBody = new JSONObject() {
            };
        }
        Long userToyIdLong = null;
        if(org.apache.commons.lang3.StringUtils.isNotBlank(userToyId)) {
            try {
                userToyIdLong = Long.parseLong(userToyId);
            } catch (NumberFormatException ignored) {
            }
        }
        Object userToyIdByRequest = this.requestBody.get(RpcConstants.USER_TOY_ID);
        if (userToyIdByRequest == null) {
            this.requestBody.put(RpcConstants.USER_TOY_ID, userToyIdLong);
        }
    }

    public void setAuthToySn(String toySn) {
        if (ObjectUtils.isEmpty(this.requestBody)) {
            this.requestBody = new JSONObject() {
            };
        }
        Object toySnByRequest = this.requestBody.get(RpcConstants.TOY_SN);
        if (toySnByRequest == null) {
            this.requestBody.put(RpcConstants.TOY_SN, toySn);
        }
    }

    public void setLongitude(String longitude) {
        if (ObjectUtils.isEmpty(this.requestBody)) {
            this.requestBody = new JSONObject() {};
        }
        this.requestBody.put(RpcConstants.LONGITUDE, longitude);
    }

    public void setLatitude(String latitude) {
        if (ObjectUtils.isEmpty(this.requestBody)) {
            this.requestBody = new JSONObject() {};
        }
        this.requestBody.put(RpcConstants.LATITUDE, latitude);
    }
    /**
     * 在响应加上tid
     *
     * @param traceId
     */
    public void fillTraceId(String traceId) {
        Object result = this.result;
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(result));
        if (jsonObject != null) {
            jsonObject.put("tid", traceId);
        } else {
            log.error("fillTraceId result is null result:{}",JSON.toJSONString(result));
        }
        this.result = jsonObject;
    }
}
