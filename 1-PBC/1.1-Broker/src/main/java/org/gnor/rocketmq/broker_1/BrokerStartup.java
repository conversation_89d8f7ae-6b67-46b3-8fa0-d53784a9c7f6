package org.gnor.rocketmq.broker_1;

import com.alibaba.fastjson2.JSON;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.concurrent.DefaultEventExecutorGroup;

import java.net.InetSocketAddress;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @version 1.0
 * @since 2025/7/1
 */
public class BrokerStartup {
    private final ServerBootstrap serverBootstrap;
    protected final EventLoopGroup eventLoopGroupSelector;
    protected final EventLoopGroup eventLoopGroupBoss;
    protected final NettyServerHandler serverHandler;

    public BrokerStartup() {
        this.serverBootstrap = new ServerBootstrap();
        this.eventLoopGroupSelector = new NioEventLoopGroup(3, new ThreadFactoryImpl("NettyServerNIOSelector_"));
        this.eventLoopGroupBoss = new NioEventLoopGroup(1, new ThreadFactoryImpl("NettyNIOBoss_"));
        this.serverHandler = new NettyServerHandler();
    }
    public void start() {
        initServerBootstrap(serverBootstrap);
        try {
            ChannelFuture sync = serverBootstrap.bind().sync();
            InetSocketAddress addr = (InetSocketAddress) sync.channel().localAddress();
            System.out.println("Broker started, listening 0.0.0.0:9111");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        BrokerStartup brokerStartup = new BrokerStartup();
        brokerStartup.start();
    }

    public static class ThreadFactoryImpl implements ThreadFactory {
        private final AtomicLong threadIndex = new AtomicLong(0);
        private final String threadNamePrefix;
        private final boolean daemon;

        public ThreadFactoryImpl(final String threadNamePrefix) {
            this(threadNamePrefix, false);
        }

        public ThreadFactoryImpl(final String threadNamePrefix, boolean daemon) {
            this.threadNamePrefix = threadNamePrefix;
            this.daemon = daemon;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, threadNamePrefix + this.threadIndex.incrementAndGet());
            thread.setDaemon(daemon);
            return thread;
        }
    }

    protected void initServerBootstrap(ServerBootstrap serverBootstrap) {
        serverBootstrap.group(this.eventLoopGroupBoss, this.eventLoopGroupSelector)
                .channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, 1024)
                .option(ChannelOption.SO_REUSEADDR, true)
                .childOption(ChannelOption.SO_KEEPALIVE, false)
                .childOption(ChannelOption.TCP_NODELAY, true)
                .localAddress(new InetSocketAddress("0.0.0.0", 9111))
                .childHandler( new ChannelInitializer<SocketChannel>() {
                    @Override
                    public void initChannel(SocketChannel ch) {
                        ch.pipeline()
                                .addLast(new DefaultEventExecutorGroup(8, new ThreadFactoryImpl("NettyServerCodecThread_")),
                                        new NettyDecoder(),
                                        new IdleStateHandler(0, 0, 120),
                                        serverHandler);

                    }
                });
    }

    public static class RemotingCommand {
        private String hey;
        private transient byte[] body;

        public String getHey() {
            return hey;
        }

        public void setHey(String hey) {
            this.hey = hey;
        }

        public static int getHeaderLength(int length) {
            return length & 0xFFFFFF;
        }

        private static RemotingCommand headerDecode(ByteBuf byteBuffer, int len, int type) {
            switch (type) {
                case 0:  //JSON
                    byte[] headerData = new byte[len];
                    byteBuffer.readBytes(headerData);
                    RemotingCommand resultJson = JSON.parseObject(headerData, RemotingCommand.class);
                    return resultJson;
                //case 1:  //ROCKETMQ
                //    RemotingCommand resultRMQ = RocketMQSerializable.rocketMQProtocolDecode(byteBuffer, len);
                //    return resultRMQ;
                default:
                    break;
            }

            return null;
        }

        public static RemotingCommand decode(final ByteBuf byteBuffer) {
            int length = byteBuffer.readableBytes();
            int oriHeaderLen = byteBuffer.readInt();
            int headerLength = getHeaderLength(oriHeaderLen);
            //if (headerLength > length - 4) {
            //    throw new RemotingCommandException("decode error, bad header length: " + headerLength);
            //}

            RemotingCommand cmd = headerDecode(byteBuffer, headerLength, (oriHeaderLen >> 24) & 0xFF);

            int bodyLength = length - 4 - headerLength;
            byte[] bodyData = null;
            if (bodyLength > 0) {
                bodyData = new byte[bodyLength];
                byteBuffer.readBytes(bodyData);
            }
            cmd.body = bodyData;

            return cmd;
        }
    }


    public static class NettyDecoder extends LengthFieldBasedFrameDecoder {
        private static final int FRAME_MAX_LENGTH = Integer.parseInt(System.getProperty("com.rocketmq.remoting.frameMaxLength", "16777216"));

        public NettyDecoder() {
            super(FRAME_MAX_LENGTH, 0, 4, 0, 4);
        }

        @Override
        public Object decode(ChannelHandlerContext ctx, ByteBuf in) throws Exception {
            ByteBuf frame = null;
            try {
                frame = (ByteBuf) super.decode(ctx, in);
                if (null == frame) {
                    return null;
                }
                RemotingCommand cmd = RemotingCommand.decode(frame);
                return cmd;
            } catch (Exception e) {
                //RemotingHelper.closeChannel(ctx.channel());
            } finally {
                if (null != frame) {
                    frame.release();
                }
            }

            return null;
        }
    }

    @ChannelHandler.Sharable
    public static class NettyServerHandler extends SimpleChannelInboundHandler<RemotingCommand> {

        @Override
        protected void channelRead0(ChannelHandlerContext channelHandlerContext, RemotingCommand remotingCommand) {
            System.out.println("Received hey:" + remotingCommand.getHey());
        }
    }

}
