package org.gnor.rocketmq.producer_1;

import com.alibaba.fastjson2.JSON;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.CharsetUtil;

public class NettyClient {
    private final String host;
    private final int port;

    public NettyClient(String host, int port) {
        this.host = host;
        this.port = port;
    }

    public void run() throws Exception {
        // 配置客户端
        EventLoopGroup workerGroup = new NioEventLoopGroup();

        try {
            Bootstrap b = new Bootstrap();
            b.group(workerGroup)
                    .channel(NioSocketChannel.class) // 使用NIO传输Channel
                    .option(ChannelOption.SO_KEEPALIVE, true) // 保持连接
                    .handler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) {
                            ch.pipeline().addLast(new ClientHandler());
                        }
                    });

            // 连接服务器
            ChannelFuture f = b.connect(host, port).sync();
            System.out.println("已连接到服务器: " + host + ":" + port);

            // 发送消息
            String message = "Hello, Netty Server!";
            ByteBuf byteBuf = Unpooled.copiedBuffer(message, CharsetUtil.UTF_8);
            f.channel().writeAndFlush(byteBuf);
            System.out.println("已发送消息: " + message);

            // 等待连接关闭
            f.channel().closeFuture().sync();
        } finally {
            workerGroup.shutdownGracefully();
        }
    }

    public static void main(String[] args) throws Exception {
        String host = "localhost";
        int port = 10911;
        new NettyClient(host, port).run();
    }

    // 客户端处理器
    static class ClientHandler extends SimpleChannelInboundHandler<ByteBuf> {
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, ByteBuf msg) {
            // 读取服务器发送的消息
            String receivedMessage = msg.toString(CharsetUtil.UTF_8);
            System.out.println("收到服务器消息: " + receivedMessage);
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            // 异常处理
            cause.printStackTrace();
            ctx.close();
        }
    }

    public static class RemotingCommand {
        private String hey;
        private transient byte[] body;

        public String getHey() {
            return hey;
        }

        public void setHey(String hey) {
            this.hey = hey;
        }

        public static int getHeaderLength(int length) {
            return length & 0xFFFFFF;
        }

        private static RemotingCommand headerDecode(ByteBuf byteBuffer, int len, int type) {
            switch (type) {
                case 0:  //JSON
                    byte[] headerData = new byte[len];
                    byteBuffer.readBytes(headerData);
                    RemotingCommand resultJson = JSON.parseObject(headerData, RemotingCommand.class);
                    return resultJson;
                //case 1:  //ROCKETMQ
                //    RemotingCommand resultRMQ = RocketMQSerializable.rocketMQProtocolDecode(byteBuffer, len);
                //    return resultRMQ;
                default:
                    break;
            }

            return null;
        }

        public static RemotingCommand decode(final ByteBuf byteBuffer) {
            int length = byteBuffer.readableBytes();
            int oriHeaderLen = byteBuffer.readInt();
            int headerLength = getHeaderLength(oriHeaderLen);
            //if (headerLength > length - 4) {
            //    throw new RemotingCommandException("decode error, bad header length: " + headerLength);
            //}

            RemotingCommand cmd = headerDecode(byteBuffer, headerLength, (oriHeaderLen >> 24) & 0xFF);

            int bodyLength = length - 4 - headerLength;
            byte[] bodyData = null;
            if (bodyLength > 0) {
                bodyData = new byte[bodyLength];
                byteBuffer.readBytes(bodyData);
            }
            cmd.body = bodyData;

            return cmd;
        }
    }
}
